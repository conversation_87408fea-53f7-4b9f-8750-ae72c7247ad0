# Gradle网络问题解决方案

## 问题描述
在构建Android应用时遇到Gradle下载超时错误：
```
Timeout of 120000 reached waiting for exclusive access to file: gradle-8.12-all.zip
```

## 解决方案

### 方案1：手动下载Gradle（推荐）
1. 访问 https://gradle.org/releases/ 下载 gradle-8.12-all.zip
2. 将下载的文件放到：`C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.12-all\ejduaidbjup3bmmkhw3rie4zb\`
3. 重新运行 `flutter build apk --release`

### 方案2：使用国内镜像
修改 `android/gradle/wrapper/gradle-wrapper.properties` 文件：
```properties
distributionUrl=https\://mirrors.cloud.tencent.com/gradle/gradle-8.12-all.zip
```

### 方案3：使用移动热点
如果公司网络有限制，可以尝试使用手机热点进行构建。

### 方案4：离线构建
1. 在有良好网络的环境下运行一次 `flutter build apk --release`
2. 将 `.gradle` 文件夹打包
3. 在目标环境解压使用

## 构建成功后的文件位置
APK文件将生成在：`build/app/outputs/flutter-apk/app-release.apk`

## 安装说明
1. 将APK文件传输到Android设备
2. 在设备上启用"未知来源"安装
3. 点击APK文件进行安装

## 注意事项
- 首次构建需要下载大量依赖，请确保网络稳定
- 如果仍有问题，建议在网络条件好的环境下进行构建
- 构建成功后，后续的增量构建会快很多