# AI口语老师 Flutter应用

这是一个Flutter应用，将AI口语老师网站 (https://talk.xstar.city/) 嵌入到移动应用中，提供便捷的AI对话功能。

## 功能特性

- 🌐 **跨平台支持**: 支持Android、iOS和Web平台
- 📱 **原生WebView**: 在移动设备上使用原生WebView组件
- 🖥️ **Web兼容**: 在Web平台使用iframe嵌入
- 🔄 **刷新功能**: 支持页面刷新
- ⚡ **加载指示**: 显示页面加载状态

## 技术栈

- **Flutter**: 跨平台UI框架
- **webview_flutter**: 移动平台WebView插件
- **dart:html & dart:ui_web**: Web平台支持

## 运行方式

### Web平台
```bash
flutter run -d edge --web-port=8080
# 或
flutter run -d chrome --web-port=8080
```

### Android平台
```bash
# 确保连接了Android设备或启动了模拟器
flutter run -d android
```

### iOS平台
```bash
# 需要在macOS上运行
flutter run -d ios
```

## 构建发布版本

### Android APK
```bash
flutter build apk --release
```

### Web版本
```bash
flutter build web --release
```

## 项目结构

```
lib/
├── main.dart              # 主应用入口，Web平台实现
└── mobile_webview.dart    # 移动平台WebView实现
```

## 配置说明

- **网络权限**: 已在`android/app/src/main/AndroidManifest.xml`中添加`INTERNET`权限
- **麦克风权限**: 
  - Android: 在`AndroidManifest.xml`中添加`RECORD_AUDIO`权限
  - iOS: 在`Info.plist`中添加`NSMicrophoneUsageDescription`权限描述
- **WebView依赖**: 在`pubspec.yaml`中添加了`webview_flutter: ^4.4.2`
- **平台适配**: 使用条件编译自动选择合适的实现方式

## 注意事项

1. 确保设备有网络连接
2. Web平台需要现代浏览器支持
3. 移动平台需要Android 5.0+或iOS 9.0+
4. 首次加载可能需要一些时间

## Android构建问题解决

如果遇到Gradle网络超时错误，请参考 [GRADLE_NETWORK_SOLUTION.md](./GRADLE_NETWORK_SOLUTION.md) 文件中的详细解决方案。

常见解决方法：
1. 使用移动热点网络
2. 手动下载Gradle文件
3. 配置国内镜像源
4. 在网络条件好的环境下构建

## 开发环境要求

- Flutter SDK 3.9.0+
- Dart SDK 3.0.0+
- Android Studio / VS Code
- 对于iOS开发需要Xcode (仅macOS)