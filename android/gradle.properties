org.gradle.jvmargs=-Xmx8G -XX:MaxMetaspaceSize=4G -XX:ReservedCodeCacheSize=512m -XX:+HeapDumpOnOutOfMemoryError -XX:+UseG1GC
android.useAndroidX=true
android.enableJetifier=true

# ç½ç»éç½®ä¼å
org.gradle.daemon=true
org.gradle.parallel=true
org.gradle.configureondemand=true
org.gradle.caching=true

# å¢å ç½ç»è¶æ¶æ¶é´
systemProp.http.connectionTimeout=600000
systemProp.http.socketTimeout=600000
systemProp.https.connectionTimeout=600000
systemProp.https.socketTimeout=600000

# Build optimization
org.gradle.workers.max=2
android.enableR8.fullMode=false

# Avoid file locking issues
org.gradle.vfs.watch=false
org.gradle.unsafe.configuration-cache=false
