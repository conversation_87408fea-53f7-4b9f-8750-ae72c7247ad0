import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'mobile_webview.dart';

void main() {
  runApp(const AITalkingApp());
}

class AITalkingApp extends StatelessWidget {
  const AITalkingApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'AI口语老师',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        appBarTheme: const AppBarTheme(
          backgroundColor: Colors.blue,
          foregroundColor: Colors.white,
        ),
      ),
      home: const MobileWebViewPage(),
      debugShowCheckedModeBanner: false,
    );
  }
}
