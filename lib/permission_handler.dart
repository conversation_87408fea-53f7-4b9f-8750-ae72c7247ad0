import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';

class PermissionHandlerWidget extends StatefulWidget {
  final Widget child;
  
  const PermissionHandlerWidget({super.key, required this.child});

  @override
  State<PermissionHandlerWidget> createState() => _PermissionHandlerWidgetState();
}

class _PermissionHandlerWidgetState extends State<PermissionHandlerWidget> {
  bool _permissionsGranted = false;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _checkAndRequestPermissions();
  }

  Future<void> _checkAndRequestPermissions() async {
    try {
      // 检查权限状态
      Map<Permission, PermissionStatus> statuses = await [
        Permission.microphone,
        Permission.camera,
        Permission.storage,
      ].request();

      bool allGranted = statuses.values.every(
        (status) => status == PermissionStatus.granted || status == PermissionStatus.limited
      );

      setState(() {
        _permissionsGranted = allGranted;
        _isLoading = false;
      });

      if (!allGranted) {
        _showPermissionDialog();
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
        _permissionsGranted = false;
      });
      _showPermissionDialog();
    }
  }

  void _showPermissionDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('权限请求'),
          content: const Text(
            'AI口语老师需要以下权限才能正常工作：\n\n'
            '• 麦克风权限：用于语音识别\n'
            '• 摄像头权限：用于视频通话功能\n'
            '• 存储权限：用于保存学习记录\n\n'
            '请在设置中手动开启这些权限。'
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _checkAndRequestPermissions();
              },
              child: const Text('重试'),
            ),
            TextButton(
              onPressed: () {
                openAppSettings();
              },
              child: const Text('打开设置'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                setState(() {
                  _permissionsGranted = true;
                });
              },
              child: const Text('继续使用'),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('正在检查权限...'),
            ],
          ),
        ),
      );
    }

    return widget.child;
  }
}
